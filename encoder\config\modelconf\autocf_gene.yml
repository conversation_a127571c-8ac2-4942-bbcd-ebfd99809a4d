optimizer:
  name: adam
  lr: 1.0e-3
  weight_decay: 0

train:
  epoch: 3000
  batch_size: 4096
  save_model: true
  loss: pairwise
  log_loss: false
  test_step: 3
  reproducible: true
  seed: 2023
  patience: 5
  trainer: autocf_trainer

test:
  metrics: [recall, ndcg]
  k: [5, 10, 20]
  batch_size: 1024

data:
  type: general_cf
  name: amazon

model:
  name: autocf_gene
  # general parameters here
  embedding_size: 32
  keep_rate: 0.2
  gt_layer: 1
  head_num: 4
  seed_num: 100
  mask_depth: 2
  fix_steps: 10

  # data-specific parameters here
  gcn_layer: 2
  reg_weight: 1.0e-6
  ssl_reg: 1
  mask_ratio: 5.0e-2
  recon_weight: 1.0e-1
  re_temperature: 0.5
  # for amazon
  amazon:
    gcn_layer: 2
    reg_weight: 1.0e-6
    ssl_reg: 1
    mask_ratio: 0.2
    recon_weight: 1.0
    re_temperature: 0.5
  # for yelp
  yelp:
    gcn_layer: 2
    reg_weight: 1.0e-8
    ssl_reg: 1
    mask_ratio: 0.2
    recon_weight: 0.1
    re_temperature: 0.1
  # for steam
  steam:
    gcn_layer: 2
    reg_weight: 1.0e-6
    ssl_reg: 5.0e-1
    mask_ratio: 0.2
    recon_weight: 1.0
    re_temperature: 0.2


