---
type: "always_apply"
---

# Spec Workflow 使用规范（RLMRec 项目）

## 1. 检查/续接进度
用户提到“继续上次”或不确定当前进度时，先调用：
- MCP 工具：spec-workflow
- 参数：action.type="check"，path="./specs"

## 2. 文档语言
- 所有文档统一使用中文（requirements/design/tasks）

## 3. 文档目录
- 统一放在 ./specs（或 ./specs/<子模块>）

## 4. 任务管理
- 推进任务时调用：
  - action.type="complete_task"
  - taskNumber="当前任务编号"（或批量）
- 按工具返回的指引继续，直到所有任务完成

## 5. 最佳实践
- 主动 check 当前进度
- 保障语言一致性
- 多模块灵活组织，单模块也可
- 任务粒度：1–2 小时/任务