You will serve as an assistant to help me determine which types of game a specific user is likely to enjoy.
I will provide you with information about games that the user has interacted, as well as his or her reviews of those games.
Here are the instructions:
1. Each interacted game will be described in JSON format, with the following attributes:
{
    "title": "the name/title of the game", (if there is no name, I will set this value to "None")
    "description": "a description of what types of users will like this game",
    "review": "the user's review on the game" (if there is no review, I will set this value to "None")
}

2. The information I will give you:
PLAYED GAMES: a list of JSON strings describing the games that the user has played.

Requirements:
1. Please provide your answer in JSON format, following this structure:
{
    "summarization": "A summarization of what types of game this user is likely to enjoy" (if you are unable to summarize it, please set this value to "None")
    "reasoning": "briefly explain your reasoning for the summarization"
}
2. Please ensure that the "summarization" is no longer than 100 words.
3. The "reasoning" has no word limits.
4. Do not provide any other text outside the JSON string.
