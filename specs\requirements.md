# RLMRec 项目 - 需求文档（requirements)

## 1. 背景与目标
RLMRec 是一个基于大语言模型（LLM）的推荐系统表示学习框架。目标是将协同过滤（CF）的交互表示与 LLM 的语义表示对齐，从而缓解传统仅依赖 ID 的信息瓶颈，提高推荐效果与泛化能力。

对齐路径：
- 对比对齐（plus/Con）：使用 InfoNCE 损失，通过 MLP 将语义嵌入映射至 CF 空间，支持双向对齐；温度参数控制对比强度。
- 生成对齐（gene/Gen）：采用掩码自编码器（MAE）策略，随机掩码节点后以 MLP 重构语义表示，实现单向生成式对齐。

当前支持基础模型：LightGCN、GCCF、SGL、SimGCL、DCCF、AutoCF；每个模型包含 base/plus/gene 三个版本。

## 2. 范围与非目标
- 范围：
  - 为上述 6 种基础模型提供统一的数据管道、训练与评测流程。
  - 实现 plus（对比对齐）与 gene（生成对齐）两条对齐范式，并与基础模型兼容。
  - 提供可复现实验脚本与配置，产出文档化的需求、设计与任务清单。
- 非目标：
  - 不涉及在线服务化与大规模召回/排序系统集成。
  - 不在本期实现多模态（图像/音频）对齐，仅聚焦文本与结构化交互。

## 3. 角色与使用场景（User Stories）
- 作为研究者，我希望在统一框架下快速对比不同对齐策略与基础模型，以验证对齐对推荐性能的提升。
- 作为工程师，我希望有标准化的数据处理、训练与评测脚本，便于在新数据集上复现实验。
- 作为产品同学，我希望看到明确的指标提升与消融结果，帮助判断投入产出。

## 4. 功能需求（Functional Requirements）
FR-1 数据管道：
- 支持常见推荐数据集（如 Yelp/Alibaba-Tianchi/ML-1M 等；具体以仓库内现有数据为准）。
- 提供负采样、划分、批处理与随机掩码（用于 gene）的可配置实现。

FR-2 语义表示与文本处理：
- 支持从物品/用户侧文本（如描述、标题、类别等）提取语义嵌入；
- 允许加载/缓存离线语义向量，或使用占位接口以便后续替换为任意文本编码器。

FR-3 对齐模块：
- plus：MLP 投影 + InfoNCE，支持温度参数、对齐方向（u→i、i→u）可配置；
- gene：随机掩码率可配置，MLP 重构语义表示，支持重构损失加权。

FR-4 训练与评测：
- 统一训练循环与早停；
- 评测指标：Recall@K、NDCG@K、HitRate@K（K∈{10,20} 可配置）；
- 结果日志与 checkpoint 保存。

FR-5 兼容性：
- 与 LightGCN/GCCF/SGL/SimGCL/DCCF/AutoCF 六模型兼容；
- 每模型均提供 base、plus、gene 三个可运行配置。

FR-6 实验与复现：
- 提供统一的命令行入口与示例配置；
- 输出实验报告模板与种子控制；
- 在 ./specs 中维护文档与任务追踪。

## 5. 数据与对齐信号
- 用户侧：历史交互序列、可能的用户 profile 文本（若无则可空）。
- 物品侧：标题/描述/类别等文本；
- 对齐信号：由文本编码器得到的语义向量（外部生成/离线缓存），与 CF 学到的嵌入进行对齐。

## 6. 验收标准（Acceptance Criteria）
- [ ] 至少在 1 个公开数据集上跑通 base/plus/gene 三版本（任选 1–2 个基础模型）。
- [ ] 指标：与 base 相比，plus 或 gene 至少一种在 Recall@20 或 NDCG@20 上取得显著提升（统计显著性以重复 3 次平均为准）。
- [ ] 训练与评测脚本可一键运行，产出日志、模型权重与结果表。
- [ ] ./specs 目录下 requirements/design/tasks 文档完整，任务进度可追踪。

## 7. 非功能性要求（NFR）
- 可复现：固定随机种子，记录依赖与版本；
- 性能：单卡消费级 GPU 可完成中等规模数据集训练；
- 代码质量：模块化、可配置、带基础单元测试（关键数据处理与对齐模块）。

## 8. 评测与里程碑
- M1（本周）：完成数据管道与 1 个基础模型的 base 版跑通；
- M2：完成 plus 与 gene 的可运行版本 + 初步对比；
- M3：补充另一个基础模型，完善指标与日志，形成实验小结；
- M4：文档完善与任务收尾，准备合并与发布。

## 9. 风险与假设
- 风险：语义向量质量不一可能影响对齐效果；不同数据集文本稀疏；资源受限导致训练时间较长。
- 缓解：提供离线缓存与轻量编码方案；先在小数据集上收敛验证；逐步扩展。
- 假设：已有可用的文本语义向量或可后续接入；基础模型代码已基本可运行。
