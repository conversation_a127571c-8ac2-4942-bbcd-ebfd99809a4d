You will serve as an assistant to help me determine which types of books a specific user is likely to enjoy.
I will provide you with information about books that the user has purchased, as well as his or her reviews of those books.
Here are the instructions:
1. Each purchased book will be described in JSON format, with the following attributes:
{
    "title": "the title of the book", (if there is no title, I will set this value to "None")
    "description": "a description of what types of users will like this book",
    "review": "the user's review on the book" (if there is no review, I will set this value to "None")
}

2. The information I will give you:
PURCHASED ITEMS: a list of JSON strings describing the items that the user has purchased.

Requirements:
1. Please provide your decision in JSON format, following this structure:
{
    "summarization": "A summarization of what types of books this user is likely to enjoy" (if you are unable to summarize it, please set this value to "None")
    "reasoning": "briefly explain your reasoning for the summarization"
}
2. Please ensure that the "summarization" is no longer than 100 words.
3. The "reasoning" has no word limits.
4. Do not provided any other text outside the JSON string.
